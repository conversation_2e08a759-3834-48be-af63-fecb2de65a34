import ctx from "../ctx";
import { enterGlobalAnimation } from "../animation/enterGlobal";
import { lineLeaveAnimation } from "../animation/load";
import { VIEW_MODE } from "../enums";
import TWEEN from "@tweenjs/tween.js";
import focusCrust from "./focusCrust";
import { globalLabelManager } from "../utils/globalViewLabelManager";

/**
 * 全局视图模块 - enterGlobalView.ts
 *
 * 职责：
 * 1. 处理从聚焦模式切换到全局视图模式
 * 2. 清理聚焦状态的视觉元素（外壳、连线等）
 * 3. 启用节点呼吸动画，增强全局视图的视觉效果
 * 4. 管理视角切换的平滑过渡动画
 *
 * 全局视图特点：
 * - 显示所有节点，无特定聚焦点
 * - 节点具有呼吸动画效果
 * - 没有聚焦连线和外壳装饰
 * - 适合总览整个知识图谱结构
 */

/**
 * 进入全局视图模式的核心函数
 *
 * 执行流程：
 * 1. 清理现有的动画补间效果
 * 2. 检查并防止重复切换
 * 3. 隐藏聚焦装饰元素（外壳、连线）
 * 4. 启用节点呼吸动画
 * 5. 更新历史栈和视角动画
 *
 * @param to 目标视角位置 [x, y, z] - 相机移动的目的地坐标
 * @returns Promise 视角切换动画的异步操作
 */
export default function enterGlobalView(to: [number, number, number]) {
  // 步骤1：清理所有正在进行的补间动画
  // 防止动画冲突，确保视图切换的流畅性
  TWEEN.removeAll();

  // 步骤2：防止重复切换到相同视图模式
  if (ctx.viewMode == VIEW_MODE.GLOBAL_VIEW) return;

  // 步骤3：更新视图模式状态
  ctx.viewMode = VIEW_MODE.GLOBAL_VIEW;

  // 步骤4：隐藏聚焦装饰元素
  focusCrust.hide(); // 隐藏聚焦节点的外壳装饰

  // 步骤5：启用所有节点的呼吸动画
  // 在全局视图中，节点呼吸动画增强视觉层次感
  ctx.pointsMesh!.breathAnimationSwitch();

  // 步骤5.5：启用全局视角标签管理器
  globalLabelManager.enable();

  // 步骤6：更新历史栈，标记进入全局视图
  ctx.focusStack?.push("null");

  // 步骤7：处理聚焦连线的退出动画
  if (ctx.focusLine) {
    ctx.focusLine.dispose(); // 释放连线资源
    // 执行连线消失动画，完成后从场景中移除
    lineLeaveAnimation(ctx.focusLine).then(() => {
      ctx.viewGroup!.remove(ctx.focusLine!);
    });
  }

  // 步骤8：执行视角切换动画
  // 平滑移动相机到目标位置
  return enterGlobalAnimation(ctx.controls!, to);
}
