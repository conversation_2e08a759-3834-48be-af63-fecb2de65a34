{"version": 3, "sources": ["../../three/examples/jsm/libs/stats.module.js"], "sourcesContent": ["var Stats = function () {\n\n\tvar mode = 0;\n\n\tvar container = document.createElement( 'div' );\n\tcontainer.style.cssText = 'position:fixed;top:0;left:0;cursor:pointer;opacity:0.9;z-index:10000';\n\tcontainer.addEventListener( 'click', function ( event ) {\n\n\t\tevent.preventDefault();\n\t\tshowPanel( ++ mode % container.children.length );\n\n\t}, false );\n\n\t//\n\n\tfunction addPanel( panel ) {\n\n\t\tcontainer.appendChild( panel.dom );\n\t\treturn panel;\n\n\t}\n\n\tfunction showPanel( id ) {\n\n\t\tfor ( var i = 0; i < container.children.length; i ++ ) {\n\n\t\t\tcontainer.children[ i ].style.display = i === id ? 'block' : 'none';\n\n\t\t}\n\n\t\tmode = id;\n\n\t}\n\n\t//\n\n\tvar beginTime = ( performance || Date ).now(), prevTime = beginTime, frames = 0;\n\n\tvar fpsPanel = addPanel( new Stats.Panel( 'FPS', '#0ff', '#002' ) );\n\tvar msPanel = addPanel( new Stats.Panel( 'MS', '#0f0', '#020' ) );\n\n\tif ( self.performance && self.performance.memory ) {\n\n\t\tvar memPanel = addPanel( new Stats.Panel( 'MB', '#f08', '#201' ) );\n\n\t}\n\n\tshowPanel( 0 );\n\n\treturn {\n\n\t\tREVISION: 16,\n\n\t\tdom: container,\n\n\t\taddPanel: addPanel,\n\t\tshowPanel: showPanel,\n\n\t\tbegin: function () {\n\n\t\t\tbeginTime = ( performance || Date ).now();\n\n\t\t},\n\n\t\tend: function () {\n\n\t\t\tframes ++;\n\n\t\t\tvar time = ( performance || Date ).now();\n\n\t\t\tmsPanel.update( time - beginTime, 200 );\n\n\t\t\tif ( time >= prevTime + 1000 ) {\n\n\t\t\t\tfpsPanel.update( ( frames * 1000 ) / ( time - prevTime ), 100 );\n\n\t\t\t\tprevTime = time;\n\t\t\t\tframes = 0;\n\n\t\t\t\tif ( memPanel ) {\n\n\t\t\t\t\tvar memory = performance.memory;\n\t\t\t\t\tmemPanel.update( memory.usedJSHeapSize / 1048576, memory.jsHeapSizeLimit / 1048576 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn time;\n\n\t\t},\n\n\t\tupdate: function () {\n\n\t\t\tbeginTime = this.end();\n\n\t\t},\n\n\t\t// Backwards Compatibility\n\n\t\tdomElement: container,\n\t\tsetMode: showPanel\n\n\t};\n\n};\n\nStats.Panel = function ( name, fg, bg ) {\n\n\tvar min = Infinity, max = 0, round = Math.round;\n\tvar PR = round( window.devicePixelRatio || 1 );\n\n\tvar WIDTH = 80 * PR, HEIGHT = 48 * PR,\n\t\tTEXT_X = 3 * PR, TEXT_Y = 2 * PR,\n\t\tGRAPH_X = 3 * PR, GRAPH_Y = 15 * PR,\n\t\tGRAPH_WIDTH = 74 * PR, GRAPH_HEIGHT = 30 * PR;\n\n\tvar canvas = document.createElement( 'canvas' );\n\tcanvas.width = WIDTH;\n\tcanvas.height = HEIGHT;\n\tcanvas.style.cssText = 'width:80px;height:48px';\n\n\tvar context = canvas.getContext( '2d' );\n\tcontext.font = 'bold ' + ( 9 * PR ) + 'px Helvetica,Arial,sans-serif';\n\tcontext.textBaseline = 'top';\n\n\tcontext.fillStyle = bg;\n\tcontext.fillRect( 0, 0, WIDTH, HEIGHT );\n\n\tcontext.fillStyle = fg;\n\tcontext.fillText( name, TEXT_X, TEXT_Y );\n\tcontext.fillRect( GRAPH_X, GRAPH_Y, GRAPH_WIDTH, GRAPH_HEIGHT );\n\n\tcontext.fillStyle = bg;\n\tcontext.globalAlpha = 0.9;\n\tcontext.fillRect( GRAPH_X, GRAPH_Y, GRAPH_WIDTH, GRAPH_HEIGHT );\n\n\treturn {\n\n\t\tdom: canvas,\n\n\t\tupdate: function ( value, maxValue ) {\n\n\t\t\tmin = Math.min( min, value );\n\t\t\tmax = Math.max( max, value );\n\n\t\t\tcontext.fillStyle = bg;\n\t\t\tcontext.globalAlpha = 1;\n\t\t\tcontext.fillRect( 0, 0, WIDTH, GRAPH_Y );\n\t\t\tcontext.fillStyle = fg;\n\t\t\tcontext.fillText( round( value ) + ' ' + name + ' (' + round( min ) + '-' + round( max ) + ')', TEXT_X, TEXT_Y );\n\n\t\t\tcontext.drawImage( canvas, GRAPH_X + PR, GRAPH_Y, GRAPH_WIDTH - PR, GRAPH_HEIGHT, GRAPH_X, GRAPH_Y, GRAPH_WIDTH - PR, GRAPH_HEIGHT );\n\n\t\t\tcontext.fillRect( GRAPH_X + GRAPH_WIDTH - PR, GRAPH_Y, PR, GRAPH_HEIGHT );\n\n\t\t\tcontext.fillStyle = bg;\n\t\t\tcontext.globalAlpha = 0.9;\n\t\t\tcontext.fillRect( GRAPH_X + GRAPH_WIDTH - PR, GRAPH_Y, PR, round( ( 1 - ( value / maxValue ) ) * GRAPH_HEIGHT ) );\n\n\t\t}\n\n\t};\n\n};\n\nexport default Stats;\n"], "mappings": ";;;AAAA,IAAI,QAAQ,WAAY;AAEvB,MAAI,OAAO;AAEX,MAAI,YAAY,SAAS,cAAe,KAAM;AAC9C,YAAU,MAAM,UAAU;AAC1B,YAAU,iBAAkB,SAAS,SAAW,OAAQ;AAEvD,UAAM,eAAe;AACrB,cAAW,EAAG,OAAO,UAAU,SAAS,MAAO;AAAA,EAEhD,GAAG,KAAM;AAIT,WAAS,SAAU,OAAQ;AAE1B,cAAU,YAAa,MAAM,GAAI;AACjC,WAAO;AAAA,EAER;AAEA,WAAS,UAAW,IAAK;AAExB,aAAU,IAAI,GAAG,IAAI,UAAU,SAAS,QAAQ,KAAO;AAEtD,gBAAU,SAAU,CAAE,EAAE,MAAM,UAAU,MAAM,KAAK,UAAU;AAAA,IAE9D;AAEA,WAAO;AAAA,EAER;AAIA,MAAI,aAAc,eAAe,MAAO,IAAI,GAAG,WAAW,WAAW,SAAS;AAE9E,MAAI,WAAW,SAAU,IAAI,MAAM,MAAO,OAAO,QAAQ,MAAO,CAAE;AAClE,MAAI,UAAU,SAAU,IAAI,MAAM,MAAO,MAAM,QAAQ,MAAO,CAAE;AAEhE,MAAK,KAAK,eAAe,KAAK,YAAY,QAAS;AAElD,QAAI,WAAW,SAAU,IAAI,MAAM,MAAO,MAAM,QAAQ,MAAO,CAAE;AAAA,EAElE;AAEA,YAAW,CAAE;AAEb,SAAO;AAAA,IAEN,UAAU;AAAA,IAEV,KAAK;AAAA,IAEL;AAAA,IACA;AAAA,IAEA,OAAO,WAAY;AAElB,mBAAc,eAAe,MAAO,IAAI;AAAA,IAEzC;AAAA,IAEA,KAAK,WAAY;AAEhB;AAEA,UAAI,QAAS,eAAe,MAAO,IAAI;AAEvC,cAAQ,OAAQ,OAAO,WAAW,GAAI;AAEtC,UAAK,QAAQ,WAAW,KAAO;AAE9B,iBAAS,OAAU,SAAS,OAAW,OAAO,WAAY,GAAI;AAE9D,mBAAW;AACX,iBAAS;AAET,YAAK,UAAW;AAEf,cAAI,SAAS,YAAY;AACzB,mBAAS,OAAQ,OAAO,iBAAiB,SAAS,OAAO,kBAAkB,OAAQ;AAAA,QAEpF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAAA,IAEA,QAAQ,WAAY;AAEnB,kBAAY,KAAK,IAAI;AAAA,IAEtB;AAAA;AAAA,IAIA,YAAY;AAAA,IACZ,SAAS;AAAA,EAEV;AAED;AAEA,MAAM,QAAQ,SAAW,MAAM,IAAI,IAAK;AAEvC,MAAI,MAAM,UAAU,MAAM,GAAG,QAAQ,KAAK;AAC1C,MAAI,KAAK,MAAO,OAAO,oBAAoB,CAAE;AAE7C,MAAI,QAAQ,KAAK,IAAI,SAAS,KAAK,IAClC,SAAS,IAAI,IAAI,SAAS,IAAI,IAC9B,UAAU,IAAI,IAAI,UAAU,KAAK,IACjC,cAAc,KAAK,IAAI,eAAe,KAAK;AAE5C,MAAI,SAAS,SAAS,cAAe,QAAS;AAC9C,SAAO,QAAQ;AACf,SAAO,SAAS;AAChB,SAAO,MAAM,UAAU;AAEvB,MAAI,UAAU,OAAO,WAAY,IAAK;AACtC,UAAQ,OAAO,UAAY,IAAI,KAAO;AACtC,UAAQ,eAAe;AAEvB,UAAQ,YAAY;AACpB,UAAQ,SAAU,GAAG,GAAG,OAAO,MAAO;AAEtC,UAAQ,YAAY;AACpB,UAAQ,SAAU,MAAM,QAAQ,MAAO;AACvC,UAAQ,SAAU,SAAS,SAAS,aAAa,YAAa;AAE9D,UAAQ,YAAY;AACpB,UAAQ,cAAc;AACtB,UAAQ,SAAU,SAAS,SAAS,aAAa,YAAa;AAE9D,SAAO;AAAA,IAEN,KAAK;AAAA,IAEL,QAAQ,SAAW,OAAO,UAAW;AAEpC,YAAM,KAAK,IAAK,KAAK,KAAM;AAC3B,YAAM,KAAK,IAAK,KAAK,KAAM;AAE3B,cAAQ,YAAY;AACpB,cAAQ,cAAc;AACtB,cAAQ,SAAU,GAAG,GAAG,OAAO,OAAQ;AACvC,cAAQ,YAAY;AACpB,cAAQ,SAAU,MAAO,KAAM,IAAI,MAAM,OAAO,OAAO,MAAO,GAAI,IAAI,MAAM,MAAO,GAAI,IAAI,KAAK,QAAQ,MAAO;AAE/G,cAAQ,UAAW,QAAQ,UAAU,IAAI,SAAS,cAAc,IAAI,cAAc,SAAS,SAAS,cAAc,IAAI,YAAa;AAEnI,cAAQ,SAAU,UAAU,cAAc,IAAI,SAAS,IAAI,YAAa;AAExE,cAAQ,YAAY;AACpB,cAAQ,cAAc;AACtB,cAAQ,SAAU,UAAU,cAAc,IAAI,SAAS,IAAI,OAAS,IAAM,QAAQ,YAAe,YAAa,CAAE;AAAA,IAEjH;AAAA,EAED;AAED;AAEA,IAAO,uBAAQ;", "names": []}